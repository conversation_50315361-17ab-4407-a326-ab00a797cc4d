"""
配置数据模型
"""

from dataclasses import dataclass
from typing import Dict


@dataclass
class ROIConfig:
    """ROI配置类"""
    name: str
    x1: int
    y1: int
    x2: int
    y2: int

    def validate(self) -> bool:
        """验证ROI配置是否有效"""
        return (
            self.x1 >= 0 and self.y1 >= 0 and
            self.x2 > self.x1 and self.y2 > self.y1
        )

    def is_within_bounds(self, width: int, height: int) -> bool:
        """检查ROI是否在图像边界内"""
        return (
            self.x1 < width and self.y1 < height and
            self.x2 <= width and self.y2 <= height
        )


@dataclass
class GameConfig:
    """游戏配置类"""
    game_id: str
    name: str
    rois: Dict[str, ROIConfig]

    def get_roi(self, roi_name: str) -> ROIConfig:
        """获取指定名称的ROI配置"""
        if roi_name not in self.rois:
            raise ValueError(f"ROI '{roi_name}' not found in game '{self.game_id}'")
        return self.rois[roi_name]


@dataclass
class OCRResult:
    """OCR识别结果类"""
    text: str
    confidence: float
    success: bool
    attempts: int
    method: str  # 'original' or 'preprocessed'
