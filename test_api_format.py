#!/usr/bin/env python3
"""
测试新的API返回格式
"""

import requests
import json

def test_ocr_api():
    """测试OCR API的新返回格式"""
    
    url = "http://localhost:5600/ocr"
    
    # 测试数据
    test_data = {
        "game_id": "fenghuangjujiang",
        "stream_url": "rtmp://**************:1935/live/test",
        "roi_name": "1pScore"
    }
    
    print("🧪 测试OCR API新返回格式...")
    print(f"📤 请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"\n📥 响应状态码: {response.status_code}")
        print(f"📥 响应头: {dict(response.headers)}")
        
        # 解析JSON响应
        try:
            response_data = response.json()
            print(f"\n📋 响应数据:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            
            # 验证新格式
            if 'code' in response_data and 'message' in response_data and 'data' in response_data:
                print(f"\n✅ 新格式验证成功!")
                print(f"   📊 Code: {response_data['code']}")
                print(f"   💬 Message: {response_data['message']}")
                print(f"   📦 Data keys: {list(response_data['data'].keys()) if isinstance(response_data['data'], dict) else 'Not a dict'}")
                
                # 根据code判断结果类型
                code = response_data['code']
                if code == 200:
                    print(f"   🎉 识别成功!")
                elif code == 801:
                    print(f"   ❌ 识别失败 (OCR错误)")
                elif code == 802:
                    print(f"   ❌ 截图失败")
                elif code == 803:
                    print(f"   ❌ 其他错误")
                else:
                    print(f"   ❓ 未知错误码: {code}")
                    
            else:
                print(f"\n❌ 格式验证失败! 缺少必要字段")
                print(f"   实际字段: {list(response_data.keys())}")
                
        except json.JSONDecodeError as e:
            print(f"\n❌ JSON解析失败: {e}")
            print(f"原始响应: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"\n❌ 请求失败: {e}")

def test_invalid_params():
    """测试无效参数的错误返回"""
    
    url = "http://localhost:5600/ocr"
    
    # 缺少必需参数
    test_data = {
        "game_id": "fenghuangjujiang"
        # 缺少 stream_url 和 roi_name
    }
    
    print(f"\n🧪 测试无效参数...")
    print(f"📤 请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, json=test_data, timeout=10)
        
        print(f"\n📥 响应状态码: {response.status_code}")
        
        response_data = response.json()
        print(f"\n📋 错误响应数据:")
        print(json.dumps(response_data, indent=2, ensure_ascii=False))
        
        # 验证错误格式
        if response_data.get('code') == 803:
            print(f"\n✅ 参数错误格式正确! (code=803)")
        else:
            print(f"\n❌ 参数错误格式不正确!")
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")

if __name__ == "__main__":
    test_ocr_api()
    test_invalid_params()
