#!/usr/bin/env python3
"""
OCR API 测试脚本
"""

import requests
import json
import sys


class APITester:
    """API测试器"""

    def __init__(self, base_url: str = "http://localhost:5600"):
        self.base_url = base_url

    def test_health(self):
        """测试健康检查接口"""
        print("=== 测试健康检查接口 ===")
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            return response.status_code == 200
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return False
        except Exception as e:
            print(f"测试失败: {e}")
            return False
        finally:
            print()

    def test_get_games(self):
        """测试获取游戏配置接口"""
        print("=== 测试获取游戏配置接口 ===")
        try:
            response = requests.get(f"{self.base_url}/games", timeout=5)
            print(f"状态码: {response.status_code}")
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")

            if response.status_code == 200 and result.get('success'):
                return list(result.get('games', {}).keys())
            return []
        except Exception as e:
            print(f"请求失败: {e}")
            return []
        finally:
            print()

    def test_get_game_rois(self, game_id: str):
        """测试获取指定游戏ROI配置接口"""
        print(f"=== 测试获取游戏 {game_id} 的ROI配置 ===")
        try:
            response = requests.get(f"{self.base_url}/games/{game_id}/rois", timeout=5)
            print(f"状态码: {response.status_code}")
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")

            if response.status_code == 200 and result.get('success'):
                return list(result.get('rois', {}).keys())
            return []
        except Exception as e:
            print(f"请求失败: {e}")
            return []
        finally:
            print()

    def test_ocr_recognition(self, game_id: str, roi_name: str, stream_url: str = None):
        """测试OCR识别接口"""
        print("=== 测试OCR识别接口 ===")

        # 使用示例流地址（如果没有提供）
        if not stream_url:
            stream_url = "rtmp://192.168.24.40/live/fenghuangjujiang"

        test_data = {
            "game_id": game_id,
            "stream_url": stream_url,
            "roi_name": roi_name,
            "max_attempts": 2,  # 减少测试时间
            "min_confidence": 0.8  # 降低阈值便于测试
        }

        try:
            print(f"请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
            response = requests.post(
                f"{self.base_url}/ocr",
                json=test_data,
                headers={"Content-Type": "application/json"},
                timeout=30  # OCR可能需要更长时间
            )
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            return response.status_code == 200
        except Exception as e:
            print(f"请求失败: {e}")
            return False
        finally:
            print()

    def test_invalid_requests(self):
        """测试无效请求"""
        print("=== 测试无效请求 ===")

        # 测试缺少参数的请求
        invalid_data = {
            "game_id": "game1",
            # 缺少 stream_url 和 roi_name
        }

        try:
            response = requests.post(
                f"{self.base_url}/ocr",
                json=invalid_data,
                headers={"Content-Type": "application/json"},
                timeout=5
            )
            print(f"缺少参数的请求:")
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"请求失败: {e}")

        # 测试不存在的游戏ID
        invalid_game_data = {
            "game_id": "nonexistent_game",
            "stream_url": "rtmp://example.com/live/stream",
            "roi_name": "score"
        }

        try:
            response = requests.post(
                f"{self.base_url}/ocr",
                json=invalid_game_data,
                headers={"Content-Type": "application/json"},
                timeout=5
            )
            print(f"\n不存在的游戏ID:")
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"请求失败: {e}")
        print()

    def run_all_tests(self, stream_url: str = None):
        """运行所有测试"""
        print("OCR API 测试脚本")
        print("请确保OCR服务正在运行 (python main.py)")
        print("=" * 60)

        # 健康检查
        if not self.test_health():
            print("❌ 健康检查失败，服务可能未启动")
            return False

        # 获取游戏配置
        games = self.test_get_games()
        if not games:
            print("❌ 无法获取游戏配置")
            return False

        # 测试第一个游戏的ROI配置
        first_game = games[0]
        rois = self.test_get_game_rois(first_game)

        # 测试无效请求
        self.test_invalid_requests()

        # 如果有ROI配置，测试OCR识别
        if rois:
            first_roi = rois[0]
            print(f"ℹ️  将使用游戏 '{first_game}' 的ROI '{first_roi}' 进行OCR测试")
            if stream_url:
                self.test_ocr_recognition(first_game, first_roi, stream_url)
            else:
                print("⚠️  跳过OCR识别测试（需要有效的流地址）")
                print(f"   如需测试，请运行: python test_api.py --stream-url <your_stream_url>")

        print("✅ 测试完成")
        return True


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='OCR API测试脚本')
    parser.add_argument(
        '--url',
        default='http://localhost:5600',
        help='API服务地址 (默认: http://localhost:5600)'
    )
    parser.add_argument(
        '--stream-url',
        help='用于OCR测试的流媒体地址'
    )

    args = parser.parse_args()

    tester = APITester(args.url)
    success = tester.run_all_tests(args.stream_url)

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
