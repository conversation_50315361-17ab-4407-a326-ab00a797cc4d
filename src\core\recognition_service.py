"""
识别服务核心逻辑
"""

import os
import time
from typing import Dict, Any
from ..config.models import OCRResult
from ..config.manager import ConfigManager
from ..utils.stream_capture import StreamCapture
from ..utils.image_processor import ImageProcessor
from .ocr_engine import OCREngine


class RecognitionService:
    """识别服务"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.stream_capture = StreamCapture()
        self.image_processor = ImageProcessor()
        self.ocr_engine = OCREngine()
    
    def recognize_from_stream(
        self,
        stream_url: str,
        game_id: str,
        roi_name: str,
        max_attempts: int = 3,
        min_confidence: float = 0.9,
        rotation_angle: int = 0
    ) -> Dict[str, Any]:
        """
        从流媒体中识别指定ROI区域的文本

        Args:
            stream_url: 流媒体地址
            game_id: 游戏ID
            roi_name: ROI名称
            max_attempts: 最大尝试次数
            min_confidence: 最小置信度阈值
            rotation_angle: 图像旋转角度 (0, 90, 180, 270)

        Returns:
            识别结果字典（包含详细耗时统计）
        """
        # 总体计时
        total_start_time = time.time()
        timing_stats = {
            "total_time": 0.0,
            "config_loading": 0.0,
            "attempts": []
        }

        try:
            # 获取配置
            config_start = time.time()
            game_config = self.config_manager.get_game_config(game_id)
            roi_config = game_config.get_roi(roi_name)
            timing_stats["config_loading"] = time.time() - config_start

            best_result = None
            temp_file_path = None

            for attempt in range(max_attempts):
                attempt_start = time.time()
                attempt_timing = {
                    "attempt_number": attempt + 1,
                    "total_attempt_time": 0.0,
                    "stream_capture": {},
                    "image_processing": {},
                    "ocr_recognition": {}
                }

                try:
                    # 每次尝试都重新截图
                    print(f"📸 第{attempt + 1}次尝试：开始新的截图...")

                    # 截取帧 - 使用渐进式截取策略
                    capture_start = time.time()
                    try:
                        # 使用渐进式截取（从最快到最稳定）
                        temp_file_path, capture_timing = self.stream_capture.progressive_capture_frame(stream_url)
                        capture_timing["mode"] = "progressive"
                        print(f"📸 第{attempt + 1}次截图完成：{temp_file_path}")
                    except Exception as progressive_error:
                        # 渐进式失败，最后尝试标准模式
                        print(f"🔄 渐进式截取失败，使用标准模式: {progressive_error}")
                        temp_file_path, capture_timing = self.stream_capture.capture_frame(stream_url)
                        capture_timing["mode"] = "standard_fallback"
                        capture_timing["progressive_error"] = str(progressive_error)
                        print(f"📸 第{attempt + 1}次截图完成（标准模式）：{temp_file_path}")

                    attempt_timing["stream_capture"] = capture_timing

                    # 加载图像
                    load_start = time.time()
                    image = self.image_processor.load_image(temp_file_path)
                    attempt_timing["image_processing"]["load_image"] = time.time() - load_start

                    # 提取ROI
                    roi_start = time.time()
                    roi_image = self.image_processor.extract_roi(image, roi_config)
                    attempt_timing["image_processing"]["extract_roi"] = time.time() - roi_start

                    # 进行OCR识别（包含原始和预处理的回退逻辑）
                    ocr_result, ocr_timing = self.ocr_engine.recognize_roi_with_fallback(
                        roi_image, min_confidence, rotation_angle
                    )
                    attempt_timing["ocr_recognition"] = ocr_timing

                    # 更新尝试次数
                    ocr_result.attempts = attempt + 1

                    # 更新最佳结果
                    if best_result is None or ocr_result.confidence > best_result.confidence:
                        best_result = ocr_result

                    # 记录本次尝试总耗时
                    attempt_timing["total_attempt_time"] = time.time() - attempt_start
                    attempt_timing["success"] = ocr_result.confidence >= min_confidence
                    attempt_timing["result"] = {
                        "text": ocr_result.text,
                        "confidence": ocr_result.confidence,
                        "method": ocr_result.method
                    }
                    timing_stats["attempts"].append(attempt_timing)

                    # 如果达到最小置信度要求，直接返回
                    if ocr_result.confidence >= min_confidence:
                        print(f"✅ 第{attempt + 1}次尝试成功！置信度 {ocr_result.confidence:.3f} >= {min_confidence}")
                        break
                    else:
                        print(f"⚠️  第{attempt + 1}次尝试置信度不足：{ocr_result.confidence:.3f} < {min_confidence}")
                        if attempt < max_attempts - 1:
                            print(f"🔄 准备进行第{attempt + 2}次尝试（重新截图）...")

                except Exception as e:
                    # 记录失败的尝试
                    attempt_timing["total_attempt_time"] = time.time() - attempt_start
                    attempt_timing["success"] = False
                    attempt_timing["error"] = str(e)
                    timing_stats["attempts"].append(attempt_timing)

                    # 判断错误类型并记录
                    error_str = str(e).lower()
                    if 'ffmpeg' in error_str or '截图' in error_str or '流媒体' in error_str:
                        error_type = "截图失败"
                    elif 'ocr' in error_str or '识别' in error_str:
                        error_type = "OCR识别失败"
                    else:
                        error_type = "其他错误"

                    print(f"❌ 第{attempt + 1}次尝试失败 ({error_type}): {e}")

                    # 记录单次尝试的错误，但继续下一次尝试
                    if attempt == max_attempts - 1:  # 最后一次尝试
                        if best_result is None:
                            raise Exception(f"所有尝试都失败了，最后错误 ({error_type}): {e}")

                finally:
                    # 注意：不清理临时文件，因为我们要保留截图历史
                    # 文件会通过StreamCapture的自动清理机制管理
                    pass

            # 记录总耗时
            timing_stats["total_time"] = time.time() - total_start_time

            # 构建返回结果
            image_h, image_w = image.shape[:2] if 'image' in locals() else (0, 0)

            return {
                "success": True,
                "game_id": game_id,
                "game_name": game_config.name,
                "roi_name": roi_name,
                "text": best_result.text,
                "confidence": best_result.confidence,
                "recognition_success": best_result.success,
                "attempts": best_result.attempts,
                "method": best_result.method,
                "image_size": f"{image_w}x{image_h}",
                "roi_coordinates": f"({roi_config.x1},{roi_config.y1},{roi_config.x2},{roi_config.y2})",
                "timing": timing_stats
            }

        except Exception as e:
            timing_stats["total_time"] = time.time() - total_start_time
            return {
                "success": False,
                "error": str(e),
                "game_id": game_id,
                "roi_name": roi_name,
                "timing": timing_stats
            }
