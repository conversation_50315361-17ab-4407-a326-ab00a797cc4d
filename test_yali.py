#!/usr/bin/env python3
"""
OCR并发连续测试脚本
保持5个识别任务同时执行，无限循环测试
"""

import requests
import json
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
import queue
import signal
import sys

class ContinuousOCRTester:
    """连续OCR并发测试器"""
    
    def __init__(self, base_url="http://localhost:5600", concurrent_tasks=5):
        self.base_url = base_url
        self.concurrent_tasks = concurrent_tasks
        self.running = True
        self.task_counter = 0
        self.results_queue = queue.Queue()
        self.stats_lock = threading.Lock()
        
        # 统计数据
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.total_time = 0.0
        self.min_time = float('inf')
        self.max_time = 0.0
        
        # 测试参数
        self.test_data = {
            "game_id": "douluodalu",
            "stream_url": "rtmp://*************/live/douluodalu001",
            "roi_name": "2pScore",
            "max_attempts": 3,
            "min_confidence": 0.9,
            "rotation_angle": 0
        }
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """信号处理器，优雅退出"""
        print(f"\n\n🛑 收到退出信号 ({signum})，正在停止测试...")
        self.running = False
    
    def single_ocr_task(self, task_id: int) -> dict:
        """单个OCR任务"""
        start_time = time.time()
        thread_id = threading.current_thread().ident
        
        # 添加500毫秒间隔，避免请求过于密集
        time.sleep(0.5)
        
        try:
            # 发送OCR请求
            response = requests.post(
                f"{self.base_url}/ocr",
                json=self.test_data,
                timeout=60  # 增加超时时间
            )
            
            request_time = time.time() - start_time
            
            # 解析响应
            success = False
            ocr_success = False
            text = ""
            confidence = 0.0
            error_msg = ""
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    
                    if response_data.get("code") == 200:
                        success = True
                        ocr_success = True
                        data = response_data.get("data", {})
                        text = data.get("text", "")
                        confidence = data.get("confidence", 0.0)
                    else:
                        error_msg = response_data.get("message", "未知错误")
                        
                except json.JSONDecodeError:
                    error_msg = "JSON解析失败"
            else:
                error_msg = f"HTTP {response.status_code}"
            
            # 构建结果
            result = {
                "task_id": task_id,
                "thread_id": thread_id,
                "request_time": request_time,
                "success": success,
                "ocr_success": ocr_success,
                "text": text,
                "confidence": confidence,
                "error": error_msg,
                "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3]
            }
            
            # 更新统计
            with self.stats_lock:
                self.total_requests += 1
                self.total_time += request_time
                self.min_time = min(self.min_time, request_time)
                self.max_time = max(self.max_time, request_time)
                
                if success:
                    self.successful_requests += 1
                else:
                    self.failed_requests += 1
            
            # 输出结果
            self.print_task_result(result)
            
            return result
            
        except Exception as e:
            request_time = time.time() - start_time
            
            result = {
                "task_id": task_id,
                "thread_id": thread_id,
                "request_time": request_time,
                "success": False,
                "ocr_success": False,
                "text": "",
                "confidence": 0.0,
                "error": str(e),
                "timestamp": datetime.now().strftime("%H:%M:%S.%f")[:-3]
            }
            
            # 更新统计
            with self.stats_lock:
                self.total_requests += 1
                self.failed_requests += 1
                self.total_time += request_time
                self.min_time = min(self.min_time, request_time)
                self.max_time = max(self.max_time, request_time)
            
            self.print_task_result(result)
            return result
    
    def print_task_result(self, result: dict):
        """打印任务结果"""
        task_id = result["task_id"]
        thread_id = result["thread_id"]
        request_time = result["request_time"]
        timestamp = result["timestamp"]
        
        if result["success"]:
            text = result["text"]
            confidence = result["confidence"]
            print(f"✅ [{timestamp}] 任务#{task_id:04d} (线程:{thread_id}) "
                  f"耗时:{request_time:.3f}s 识别:'{text}' 置信度:{confidence:.3f}")
        else:
            error = result["error"]
            print(f"❌ [{timestamp}] 任务#{task_id:04d} (线程:{thread_id}) "
                  f"耗时:{request_time:.3f}s 错误:{error}")
    
    def print_stats(self):
        """打印统计信息"""
        with self.stats_lock:
            if self.total_requests == 0:
                return
                
            avg_time = self.total_time / self.total_requests
            success_rate = (self.successful_requests / self.total_requests) * 100
            
            print(f"\n📊 实时统计 (总计:{self.total_requests}次)")
            print(f"   成功率: {success_rate:.1f}% ({self.successful_requests}/{self.total_requests})")
            print(f"   平均耗时: {avg_time:.3f}s")
            print(f"   最快: {self.min_time:.3f}s, 最慢: {self.max_time:.3f}s")
            print(f"   失败次数: {self.failed_requests}")
    
    def stats_reporter(self):
        """统计报告线程"""
        last_report_time = time.time()
        
        while self.running:
            time.sleep(1)
            
            # 每10秒打印一次统计
            if time.time() - last_report_time >= 10:
                self.print_stats()
                last_report_time = time.time()
    
    def run_continuous_test(self):
        """运行连续测试"""
        print("🚀 OCR并发连续测试")
        print("=" * 80)
        print(f"📋 测试参数:")
        print(f"   并发任务数: {self.concurrent_tasks}")
        print(f"   服务地址: {self.base_url}")
        print(f"   游戏ID: {self.test_data['game_id']}")
        print(f"   流地址: {self.test_data['stream_url']}")
        print(f"   ROI名称: {self.test_data['roi_name']}")
        print(f"   旋转角度: {self.test_data['rotation_angle']}°")
        print("=" * 80)
        print("按 Ctrl+C 停止测试\n")
        
        # 启动统计报告线程
        stats_thread = threading.Thread(target=self.stats_reporter, daemon=True)
        stats_thread.start()
        
        # 使用线程池保持固定数量的并发任务
        with ThreadPoolExecutor(max_workers=self.concurrent_tasks) as executor:
            # 提交初始任务
            futures = {}
            for i in range(self.concurrent_tasks):
                self.task_counter += 1
                future = executor.submit(self.single_ocr_task, self.task_counter)
                futures[future] = self.task_counter
            
            
            # 持续循环，保持并发任务数量
            while self.running:
                try:
                    # 等待任何一个任务完成
                    completed_futures = []
                    for future in as_completed(futures.keys(), timeout=1.0):
                        completed_futures.append(future)
                        
                        # 立即提交新任务替换完成的任务
                        if self.running:
                            self.task_counter += 1
                            new_future = executor.submit(self.single_ocr_task, self.task_counter)
                            futures[new_future] = self.task_counter
                    
                    # 清理完成的任务
                    for future in completed_futures:
                        del futures[future]
                        
                except Exception as e:
                    if self.running:  # 只在运行时打印错误
                        print(f"⚠️  任务调度异常: {e}")
                    time.sleep(0.1)
        
        # 打印最终统计
        print(f"\n🏁 测试结束")
        self.print_stats()

def main():
    """主函数"""
    
    # 检查服务可用性
    base_url = "http://localhost:5600"
    
    try:
        print("🔍 检查OCR服务状态...")
        response = requests.get(f"{base_url}/health", timeout=20)
        if response.status_code == 200:
            print("✅ OCR服务可用")
        else:
            print(f"⚠️  OCR服务响应异常: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 无法连接到OCR服务: {e}")
        print("请确保OCR服务正在运行 (python main.py)")
        return
    
    # 创建并运行测试器
    tester = ContinuousOCRTester(base_url=base_url, concurrent_tasks=3)
    
    try:
        tester.run_continuous_test()
    except KeyboardInterrupt:
        print(f"\n👋 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
    finally:
        tester.running = False

if __name__ == "__main__":
    main()
