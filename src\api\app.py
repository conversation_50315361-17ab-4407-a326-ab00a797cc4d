"""
Flask应用程序
"""

from flask import Flask
from ..config.manager import Config<PERSON>anager
from ..core.recognition_service import RecognitionService
from .routes import create_routes


def create_app(config_file: str = "game_configs.json") -> Flask:
    """创建Flask应用"""
    
    app = Flask(__name__)
    
    try:
        # 初始化配置管理器
        config_manager = ConfigManager(config_file)
        
        # 初始化识别服务
        recognition_service = RecognitionService(config_manager)
        
        # 注册路由
        api_routes = create_routes(recognition_service)
        app.register_blueprint(api_routes)
        
        # 添加应用上下文
        app.config['recognition_service'] = recognition_service
        app.config['config_manager'] = config_manager
        
        return app
        
    except Exception as e:
        # 如果初始化失败，创建一个只返回错误信息的应用
        error_app = Flask(__name__)
        
        @error_app.route('/', defaults={'path': ''})
        @error_app.route('/<path:path>')
        def catch_all(path):
            from flask import jsonify
            return jsonify({
                "success": False,
                "error": f"服务初始化失败: {str(e)}",
                "message": "请检查配置文件是否存在且格式正确"
            }), 500
        
        return error_app
