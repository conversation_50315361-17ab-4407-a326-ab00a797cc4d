#!/usr/bin/env python3
"""
并发OCR测试脚本
测试多线程环境下OCR服务的稳定性和线程安全性
"""

import requests
import json
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import statistics

class ConcurrentOCRTester:
    """并发OCR测试器"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.results = []
        self.errors = []
        self.lock = threading.Lock()
    
    def single_ocr_request(self, request_id: int, test_data: dict) -> dict:
        """单个OCR请求"""
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{self.base_url}/ocr", 
                json=test_data, 
                timeout=30
            )
            
            request_time = time.time() - start_time
            
            result = {
                "request_id": request_id,
                "status_code": response.status_code,
                "request_time": request_time,
                "success": response.status_code == 200,
                "thread_id": threading.current_thread().ident
            }
            
            try:
                response_data = response.json()
                result["response_data"] = response_data
                
                # 检查新的返回格式
                if "code" in response_data:
                    result["response_code"] = response_data["code"]
                    result["message"] = response_data.get("message", "")
                    
                    if response_data["code"] == 200:
                        result["ocr_success"] = True
                        if "data" in response_data and response_data["data"]:
                            result["text"] = response_data["data"].get("text", "")
                            result["confidence"] = response_data["data"].get("confidence", 0.0)
                    else:
                        result["ocr_success"] = False
                        
            except json.JSONDecodeError:
                result["json_error"] = True
                result["raw_response"] = response.text
            
            with self.lock:
                self.results.append(result)
                
            print(f"✅ 请求 {request_id} 完成 (线程: {threading.current_thread().ident}, 耗时: {request_time:.3f}s)")
            return result
            
        except Exception as e:
            error_result = {
                "request_id": request_id,
                "error": str(e),
                "request_time": time.time() - start_time,
                "thread_id": threading.current_thread().ident
            }
            
            with self.lock:
                self.errors.append(error_result)
                
            print(f"❌ 请求 {request_id} 失败: {e}")
            return error_result
    
    def test_concurrent_requests(self, num_requests: int = 10, max_workers: int = 5):
        """测试并发请求"""
        
        print(f"🧪 开始并发测试: {num_requests} 个请求, {max_workers} 个并发线程")
        print("=" * 60)
        
        # 测试数据
        test_data = {
            "game_id": "fenghuangjujiang",
            "stream_url": "rtmp://192.168.25.174:1935/live/test",
            "roi_name": "1pScore",
            "rotation_angle": 0  # 测试新的旋转参数
        }
        
        start_time = time.time()
        
        # 使用线程池执行并发请求
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            futures = [
                executor.submit(self.single_ocr_request, i, test_data.copy())
                for i in range(num_requests)
            ]
            
            # 等待所有任务完成
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    print(f"❌ 任务执行异常: {e}")
        
        total_time = time.time() - start_time
        
        # 分析结果
        self.analyze_results(total_time)
    
    def analyze_results(self, total_time: float):
        """分析测试结果"""
        
        print("\n" + "=" * 60)
        print("📊 测试结果分析")
        print("=" * 60)
        
        total_requests = len(self.results) + len(self.errors)
        successful_requests = len([r for r in self.results if r.get("success", False)])
        failed_requests = len(self.errors) + len([r for r in self.results if not r.get("success", False)])
        
        print(f"📈 总体统计:")
        print(f"   总请求数: {total_requests}")
        print(f"   成功请求: {successful_requests}")
        print(f"   失败请求: {failed_requests}")
        print(f"   成功率: {successful_requests/total_requests*100:.1f}%")
        print(f"   总耗时: {total_time:.3f}s")
        print(f"   平均QPS: {total_requests/total_time:.2f}")
        
        # 响应时间统计
        if self.results:
            response_times = [r["request_time"] for r in self.results if "request_time" in r]
            if response_times:
                print(f"\n⏱️  响应时间统计:")
                print(f"   平均响应时间: {statistics.mean(response_times):.3f}s")
                print(f"   最快响应时间: {min(response_times):.3f}s")
                print(f"   最慢响应时间: {max(response_times):.3f}s")
                print(f"   响应时间中位数: {statistics.median(response_times):.3f}s")
        
        # 线程分布统计
        thread_ids = set()
        for result in self.results:
            if "thread_id" in result:
                thread_ids.add(result["thread_id"])
        
        print(f"\n🧵 线程统计:")
        print(f"   使用的线程数: {len(thread_ids)}")
        print(f"   线程ID列表: {sorted(thread_ids)}")
        
        # OCR结果统计
        ocr_success_count = len([r for r in self.results if r.get("ocr_success", False)])
        print(f"\n🔤 OCR识别统计:")
        print(f"   OCR成功次数: {ocr_success_count}")
        print(f"   OCR成功率: {ocr_success_count/len(self.results)*100:.1f}%" if self.results else "   OCR成功率: 0%")
        
        # 错误分析
        if self.errors:
            print(f"\n❌ 错误分析:")
            error_types = {}
            for error in self.errors:
                error_msg = error.get("error", "Unknown")
                error_types[error_msg] = error_types.get(error_msg, 0) + 1
            
            for error_type, count in error_types.items():
                print(f"   {error_type}: {count} 次")
        
        # 响应码统计
        response_codes = {}
        for result in self.results:
            code = result.get("response_code", result.get("status_code", "Unknown"))
            response_codes[code] = response_codes.get(code, 0) + 1
        
        if response_codes:
            print(f"\n📋 响应码统计:")
            for code, count in sorted(response_codes.items()):
                print(f"   {code}: {count} 次")

def main():
    """主函数"""
    
    print("🚀 OCR并发测试工具")
    print("=" * 60)
    
    # 检查服务是否可用
    tester = ConcurrentOCRTester()
    
    try:
        response = requests.get(f"{tester.base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ OCR服务可用")
        else:
            print(f"⚠️  OCR服务响应异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 无法连接到OCR服务: {e}")
        print("请确保OCR服务正在运行 (python main.py)")
        return
    
    # 执行并发测试
    print("\n开始并发测试...")
    tester.test_concurrent_requests(
        num_requests=20,  # 20个请求
        max_workers=8     # 8个并发线程
    )

if __name__ == "__main__":
    main()

