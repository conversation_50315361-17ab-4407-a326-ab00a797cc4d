"""
配置管理器
"""

import json
import os
from typing import Dict, Optional
from .models import GameConfig, ROIConfig


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str):
        self.config_file = config_file
        self.games: Dict[str, GameConfig] = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            if not config_data:
                raise ValueError("配置文件为空")
            
            for game_id, game_data in config_data.items():
                if not isinstance(game_data, dict):
                    raise ValueError(f"游戏配置格式错误: {game_id}")
                
                if 'rois' not in game_data:
                    raise ValueError(f"游戏 {game_id} 缺少 rois 配置")
                
                rois = {}
                for roi_name, roi_data in game_data['rois'].items():
                    if not all(key in roi_data for key in ['x1', 'y1', 'x2', 'y2']):
                        raise ValueError(f"ROI {roi_name} 配置不完整")
                    
                    roi_config = ROIConfig(
                        name=roi_name,
                        x1=roi_data['x1'],
                        y1=roi_data['y1'],
                        x2=roi_data['x2'],
                        y2=roi_data['y2']
                    )
                    
                    if not roi_config.validate():
                        raise ValueError(f"ROI {roi_name} 坐标无效")
                    
                    rois[roi_name] = roi_config
                
                self.games[game_id] = GameConfig(
                    game_id=game_id,
                    name=game_data.get('name', game_id),
                    rois=rois
                )
                
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件JSON格式错误: {e}")
        except Exception as e:
            raise ValueError(f"加载配置文件失败: {e}")
    
    def get_game_config(self, game_id: str) -> GameConfig:
        """获取游戏配置"""
        if game_id not in self.games:
            available_games = list(self.games.keys())
            raise ValueError(f"游戏配置不存在: {game_id}. 可用游戏: {available_games}")
        return self.games[game_id]
    
    def get_roi_config(self, game_id: str, roi_name: str) -> ROIConfig:
        """获取ROI配置"""
        game_config = self.get_game_config(game_id)
        return game_config.get_roi(roi_name)
    
    def list_games(self) -> Dict[str, str]:
        """列出所有游戏"""
        return {game_id: config.name for game_id, config in self.games.items()}
    
    def list_rois(self, game_id: str) -> Dict[str, Dict]:
        """列出指定游戏的所有ROI"""
        game_config = self.get_game_config(game_id)
        return {
            roi_name: {
                'x1': roi.x1,
                'y1': roi.y1,
                'x2': roi.x2,
                'y2': roi.y2
            }
            for roi_name, roi in game_config.rois.items()
        }
