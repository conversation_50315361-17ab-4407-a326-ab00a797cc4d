#!/usr/bin/env python3
"""
OCR识别服务主入口
"""

import sys
import os
import argparse
from src.api.app import create_app


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='OCR识别服务')
    parser.add_argument(
        '--config', 
        default='game_configs.json',
        help='配置文件路径 (默认: game_configs.json)'
    )
    parser.add_argument(
        '--host', 
        default='0.0.0.0',
        help='服务器主机地址 (默认: 0.0.0.0)'
    )
    parser.add_argument(
        '--port', 
        type=int, 
        default=5600,
        help='服务器端口 (默认: 5600)'
    )
    parser.add_argument(
        '--debug', 
        action='store_true',
        help='启用调试模式'
    )
    
    args = parser.parse_args()
    
    # 检查配置文件是否存在
    if not os.path.exists(args.config):
        print(f"❌ 错误: 配置文件不存在: {args.config}")
        print("\n请创建配置文件，格式示例:")
        print("""
{
  "game1": {
    "name": "示例游戏1",
    "rois": {
      "score": {"x1": 100, "y1": 50, "x2": 200, "y2": 80}
    }
  }
}
        """)
        sys.exit(1)
    
    try:
        # 创建Flask应用
        app = create_app(args.config)
        
        print("=" * 60)
        print("🚀 OCR数字识别服务")
        print("=" * 60)
        print(f"📁 配置文件: {args.config}")
        print(f"🌐 服务地址: http://{args.host}:{args.port}")
        print(f"🔧 调试模式: {'开启' if args.debug else '关闭'}")
        
        # 显示已加载的游戏配置
        if hasattr(app.config.get('config_manager'), 'games'):
            games = app.config['config_manager'].games
            print(f"🎮 已加载游戏: {list(games.keys())}")
        
        print("\n📚 可用接口:")
        print("  POST /ocr                    - OCR识别")
        print("  GET  /games                  - 获取所有游戏配置")
        print("  GET  /games/<id>/rois        - 获取游戏ROI配置")
        print("  GET  /health                 - 健康检查")
        print("\n按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动服务
        app.run(
            host=args.host,
            port=args.port,
            debug=args.debug,
            use_reloader=False  # 避免重复加载OCR模型
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 服务启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
