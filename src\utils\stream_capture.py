"""
流媒体截取工具
"""

import subprocess
import tempfile
import os
import time
import glob
from typing import Optional, Dict
from datetime import datetime


class StreamCapture:
    """流媒体截取器"""

    def __init__(self, timeout: int = 5, max_images: int = 300):  # 减少默认超时时间
        self.timeout = timeout
        self.max_images = max_images
        self._last_stream_url = None
        self._connection_cache_time = 0
        self._cache_duration = 30  # 连接缓存30秒

        # 创建固定的截图目录
        self.screenshot_dir = os.path.join(os.getcwd(), 'tmp', 'screenshots')
        os.makedirs(self.screenshot_dir, exist_ok=True)

    def _cleanup_old_images(self):
        """清理超过数量限制的旧图片"""
        try:
            # 获取所有jpg文件
            image_files = glob.glob(os.path.join(self.screenshot_dir, '*.jpg'))

            if len(image_files) <= self.max_images:
                return

            # 按修改时间排序（最旧的在前）
            image_files.sort(key=lambda x: os.path.getmtime(x))

            # 计算需要删除的文件数量
            files_to_delete = len(image_files) - self.max_images

            # 删除最旧的文件
            for i in range(files_to_delete):
                try:
                    os.unlink(image_files[i])
                    print(f"🗑️  删除旧截图: {os.path.basename(image_files[i])}")
                except Exception as e:
                    print(f"⚠️  删除文件失败 {image_files[i]}: {e}")

        except Exception as e:
            print(f"⚠️  清理旧图片失败: {e}")

    def _generate_filename(self) -> str:
        """生成带时间戳的文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 精确到毫秒
        return f"screenshot_{timestamp}.jpg"
    
    def capture_frame(self, stream_url: str) -> tuple[Optional[str], Dict[str, float]]:
        """
        从流中截取一帧并保存为临时文件

        Args:
            stream_url: 流媒体地址

        Returns:
            (临时文件路径, 耗时统计), 失败返回(None, 耗时统计)
        """
        timing = {}
        start_time = time.time()

        # 清理旧图片
        self._cleanup_old_images()

        # 生成文件路径
        filename = self._generate_filename()
        temp_path = os.path.join(self.screenshot_dir, filename)  # 关闭文件描述符，让ffmpeg写入

        try:
            # 优化但稳定的参数配置
            cmd = [
                'ffmpeg',
                '-fflags', '+genpts',  # 生成时间戳
                '-probesize', '1048576',  # 1MB探测大小（平衡速度和稳定性）
                '-analyzeduration', '1000000',  # 1秒分析时间
            ]

            # 根据流URL类型添加特定参数
            if 'rtmp://' in stream_url.lower():
                cmd.extend([
                    '-rtmp_live', 'live',  # RTMP直播模式
                ])
            elif 'rtsp://' in stream_url.lower():
                cmd.extend([
                    '-rtsp_transport', 'tcp',  # TCP传输更稳定
                ])

            cmd.extend([
                '-i', stream_url,
                '-vframes', '1',  # 只截取一帧
                '-q:v', '2',  # 高质量
                '-y',  # 覆盖输出文件
                '-loglevel', 'error',  # 只显示错误信息
                '-timeout', str(self.timeout * 1000000),  # 微秒
                temp_path
            ])

            # 记录ffmpeg执行时间
            ffmpeg_start = time.time()
            result = subprocess.run(
                cmd,
                capture_output=True,
                timeout=self.timeout,
                text=True
            )
            ffmpeg_end = time.time()
            timing['ffmpeg_execution'] = ffmpeg_end - ffmpeg_start

            if result.returncode == 0 and os.path.exists(temp_path):
                # 检查文件是否有内容
                if os.path.getsize(temp_path) > 0:
                    timing['total_capture'] = time.time() - start_time
                    return temp_path, timing
                else:
                    timing['total_capture'] = time.time() - start_time
                    raise Exception("截取的文件为空")
            else:
                error_msg = result.stderr if result.stderr else "未知错误"
                timing['total_capture'] = time.time() - start_time
                raise Exception(f"ffmpeg执行失败: {error_msg}")

        except subprocess.TimeoutExpired:
            timing['total_capture'] = time.time() - start_time
            timing['timeout'] = True
            raise Exception(f"截取超时 ({self.timeout}秒)")
        except FileNotFoundError:
            timing['total_capture'] = time.time() - start_time
            raise Exception("ffmpeg未安装或不在PATH中")
        except Exception as e:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.unlink(temp_path)
            timing['total_capture'] = time.time() - start_time
            raise e

    def fast_capture_frame(self, stream_url: str) -> tuple[Optional[str], Dict[str, float]]:
        """
        快速截取模式 - 使用更激进的参数

        Args:
            stream_url: 流媒体地址

        Returns:
            (临时文件路径, 耗时统计)
        """
        timing = {}
        start_time = time.time()

        # 清理旧图片
        self._cleanup_old_images()

        # 生成文件路径
        filename = self._generate_filename()
        temp_path = os.path.join(self.screenshot_dir, filename)

        try:
            # 快速截取命令 - 平衡速度和稳定性
            cmd = [
                'ffmpeg',
                '-fflags', '+genpts',
                '-probesize', '524288',  # 512KB探测大小
                '-analyzeduration', '500000',  # 0.5秒分析时间
            ]

            # 根据协议优化
            if 'rtmp://' in stream_url.lower():
                cmd.extend(['-rtmp_live', 'live'])
            elif 'rtsp://' in stream_url.lower():
                cmd.extend(['-rtsp_transport', 'tcp'])

            cmd.extend([
                '-i', stream_url,
                '-vframes', '1',
                '-q:v', '3',  # 中等质量
                '-y',
                '-loglevel', 'error',
                '-timeout', str(3 * 1000000),  # 3秒超时
                temp_path
            ])

            # 记录ffmpeg执行时间
            ffmpeg_start = time.time()
            result = subprocess.run(
                cmd,
                capture_output=True,
                timeout=3,  # 硬超时3秒
                text=True
            )
            ffmpeg_end = time.time()
            timing['ffmpeg_execution'] = ffmpeg_end - ffmpeg_start

            if result.returncode == 0 and os.path.exists(temp_path):
                if os.path.getsize(temp_path) > 0:
                    timing['total_capture'] = time.time() - start_time
                    return temp_path, timing
                else:
                    timing['total_capture'] = time.time() - start_time
                    raise Exception("截取的文件为空")
            else:
                error_msg = result.stderr if result.stderr else "未知错误"
                timing['total_capture'] = time.time() - start_time
                raise Exception(f"ffmpeg执行失败: {error_msg}")

        except subprocess.TimeoutExpired:
            timing['total_capture'] = time.time() - start_time
            timing['timeout'] = True
            raise Exception("快速截取超时 (3秒)")
        except FileNotFoundError:
            timing['total_capture'] = time.time() - start_time
            raise Exception("ffmpeg未安装或不在PATH中")
        except Exception as e:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.unlink(temp_path)
            timing['total_capture'] = time.time() - start_time
            raise e

    def progressive_capture_frame(self, stream_url: str) -> tuple[Optional[str], Dict[str, float]]:
        """
        渐进式截取 - 从最快参数开始，逐步回退到更稳定的参数

        Args:
            stream_url: 流媒体地址

        Returns:
            (临时文件路径, 耗时统计)
        """
        timing = {}
        start_time = time.time()

        # 清理旧图片
        self._cleanup_old_images()

        # 定义不同级别的参数配置
        configs = [
            {
                'name': 'ultra_fast',
                'probesize': '32768',      # 32KB
                'analyzeduration': '100000',  # 0.1秒
                'timeout': 2,
                'quality': '5'
            },
            {
                'name': 'fast',
                'probesize': '262144',     # 256KB
                'analyzeduration': '500000',  # 0.5秒
                'timeout': 3,
                'quality': '3'
            },
            {
                'name': 'balanced',
                'probesize': '1048576',    # 1MB
                'analyzeduration': '1000000', # 1秒
                'timeout': 5,
                'quality': '2'
            }
        ]

        last_error = None

        for config in configs:
            try:
                # 生成文件路径
                filename = self._generate_filename()
                temp_path = os.path.join(self.screenshot_dir, filename)

                # 构建命令
                cmd = [
                    'ffmpeg',
                    '-fflags', '+genpts',
                    '-probesize', config['probesize'],
                    '-analyzeduration', config['analyzeduration'],
                ]

                # 协议特定参数
                if 'rtmp://' in stream_url.lower():
                    cmd.extend(['-rtmp_live', 'live'])
                elif 'rtsp://' in stream_url.lower():
                    cmd.extend(['-rtsp_transport', 'tcp'])

                cmd.extend([
                    '-i', stream_url,
                    '-vframes', '1',
                    '-q:v', config['quality'],
                    '-y',
                    '-loglevel', 'error',
                    '-timeout', str(config['timeout'] * 1000000),
                    temp_path
                ])

                # 执行命令
                attempt_start = time.time()
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    timeout=config['timeout'] + 1,
                    text=True
                )
                attempt_time = time.time() - attempt_start

                timing[f'{config["name"]}_attempt'] = attempt_time

                if result.returncode == 0 and os.path.exists(temp_path):
                    if os.path.getsize(temp_path) > 0:
                        timing['total_capture'] = time.time() - start_time
                        timing['successful_mode'] = config['name']
                        timing['ffmpeg_execution'] = attempt_time
                        return temp_path, timing
                    else:
                        os.unlink(temp_path)
                        last_error = f"{config['name']}模式: 截取的文件为空"
                else:
                    if os.path.exists(temp_path):
                        os.unlink(temp_path)
                    error_msg = result.stderr if result.stderr else "未知错误"
                    last_error = f"{config['name']}模式: {error_msg}"

            except subprocess.TimeoutExpired:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                last_error = f"{config['name']}模式: 超时"
                timing[f'{config["name"]}_timeout'] = True
            except Exception as e:
                if 'temp_path' in locals() and os.path.exists(temp_path):
                    os.unlink(temp_path)
                last_error = f"{config['name']}模式: {str(e)}"

        # 所有模式都失败了
        timing['total_capture'] = time.time() - start_time
        raise Exception(f"所有截取模式都失败了，最后错误: {last_error}")
    
    def cleanup_temp_file(self, file_path: str):
        """清理临时文件（现在不删除，因为我们要保留截图历史）"""
        # 不再删除文件，保留截图历史
        # 文件会通过 _cleanup_old_images 自动管理
        pass

    def get_screenshot_count(self) -> int:
        """获取当前截图数量"""
        try:
            image_files = glob.glob(os.path.join(self.screenshot_dir, '*.jpg'))
            return len(image_files)
        except Exception:
            return 0

    def get_screenshot_dir(self) -> str:
        """获取截图目录路径"""
        return self.screenshot_dir
