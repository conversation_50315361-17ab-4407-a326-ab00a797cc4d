"""
API路由定义
"""

import time
from flask import Blueprint, request, jsonify
from ..core.recognition_service import RecognitionService


def create_routes(recognition_service: RecognitionService) -> Blueprint:
    """创建API路由"""
    
    api = Blueprint('api', __name__)
    
    @api.route('/ocr', methods=['POST'])
    def ocr_endpoint():
        """OCR识别接口"""
        request_start_time = time.time()

        try:
            data = request.get_json()

            # 验证必需参数
            required_params = ['game_id', 'stream_url', 'roi_name']
            for param in required_params:
                if param not in data:
                    return jsonify({
                        "code": 803,
                        "message": f"缺少必需参数: {param}",
                        "data": {
                            "required_params": required_params,
                            "request_time": time.time() - request_start_time
                        }
                    }), 400

            game_id = data['game_id']
            stream_url = data['stream_url']
            roi_name = data['roi_name']

            # 可选参数
            max_attempts = data.get('max_attempts', 3)
            min_confidence = data.get('min_confidence', 0.9)
            rotation_angle = data.get('rotation_angle', 0)

            # 参数验证
            if max_attempts < 1 or max_attempts > 10:
                return jsonify({
                    "code": 803,
                    "message": "max_attempts 必须在 1-10 之间",
                    "data": {
                        "request_time": time.time() - request_start_time
                    }
                }), 400

            if min_confidence < 0.0 or min_confidence > 1.0:
                return jsonify({
                    "code": 803,
                    "message": "min_confidence 必须在 0.0-1.0 之间",
                    "data": {
                        "request_time": time.time() - request_start_time
                    }
                }), 400

            if rotation_angle not in [0, 90, 180, 270]:
                return jsonify({
                    "code": 803,
                    "message": "rotation_angle 必须是 0, 90, 180, 270 中的一个",
                    "data": {
                        "request_time": time.time() - request_start_time
                    }
                }), 400

            # 处理OCR识别
            result = recognition_service.recognize_from_stream(
                stream_url, game_id, roi_name, max_attempts, min_confidence, rotation_angle
            )

            # 添加请求总耗时
            result["request_time"] = time.time() - request_start_time

            # 打印耗时统计到控制台
            print("\n" + "="*60)
            print(f"🔍 OCR识别请求完成 - {game_id}/{roi_name}")
            print("="*60)
            print(f"📊 总请求耗时: {result['request_time']:.3f}s")

            # 显示截图目录信息
            try:
                from ..core.recognition_service import RecognitionService
                service = RecognitionService()
                screenshot_count = service.stream_capture.get_screenshot_count()
                screenshot_dir = service.stream_capture.get_screenshot_dir()
                print(f"📸 截图目录: {screenshot_dir}")
                print(f"📁 当前截图数量: {screenshot_count}/300")
            except Exception:
                pass

            if 'timing' in result:
                timing = result['timing']
                print(f"⚙️  配置加载: {timing.get('config_loading', 0):.3f}s")
                print(f"🔄 总尝试次数: {len(timing.get('attempts', []))}")

                for i, attempt in enumerate(timing.get('attempts', []), 1):
                    print(f"\n📋 第{i}次尝试 ({'成功' if attempt.get('success') else '失败'}):")
                    print(f"   ⏱️  总耗时: {attempt.get('total_attempt_time', 0):.3f}s")

                    # 流媒体截取耗时
                    stream_timing = attempt.get('stream_capture', {})
                    if stream_timing:
                        mode = stream_timing.get('mode', 'unknown')
                        mode_icons = {
                            "progressive": "🚀",
                            "standard": "🐌",
                            "standard_fallback": "🔄",
                            "fast": "⚡"
                        }
                        mode_icon = mode_icons.get(mode, "❓")
                        print(f"   📹 流媒体截取 ({mode_icon}{mode}): {stream_timing.get('total_capture', 0):.3f}s")

                        if 'ffmpeg_execution' in stream_timing:
                            print(f"      - ffmpeg执行: {stream_timing['ffmpeg_execution']:.3f}s")

                        # 显示成功的模式
                        if 'successful_mode' in stream_timing:
                            successful_mode = stream_timing['successful_mode']
                            mode_names = {
                                'ultra_fast': '超快速',
                                'fast': '快速',
                                'balanced': '平衡'
                            }
                            print(f"      - 成功模式: {mode_names.get(successful_mode, successful_mode)}")

                        # 显示各种错误信息
                        if 'progressive_error' in stream_timing:
                            print(f"      - 渐进式失败: {stream_timing['progressive_error']}")
                        if 'fast_error' in stream_timing:
                            print(f"      - 快速模式失败: {stream_timing['fast_error']}")

                        # 显示各模式尝试耗时
                        for mode_name in ['ultra_fast', 'fast', 'balanced']:
                            attempt_key = f'{mode_name}_attempt'
                            timeout_key = f'{mode_name}_timeout'
                            if attempt_key in stream_timing:
                                status = " (超时)" if stream_timing.get(timeout_key) else ""
                                print(f"      - {mode_names.get(mode_name, mode_name)}尝试: {stream_timing[attempt_key]:.3f}s{status}")

                    # 图像处理耗时
                    img_timing = attempt.get('image_processing', {})
                    if img_timing:
                        total_img = sum(img_timing.values())
                        print(f"   🖼️  图像处理: {total_img:.3f}s")
                        if 'load_image' in img_timing:
                            print(f"      - 加载图像: {img_timing['load_image']:.3f}s")
                        if 'extract_roi' in img_timing:
                            print(f"      - 提取ROI: {img_timing['extract_roi']:.3f}s")

                    # OCR识别耗时
                    ocr_timing = attempt.get('ocr_recognition', {})
                    if ocr_timing:
                        print(f"   🔤 OCR识别: {ocr_timing.get('total_recognition', 0):.3f}s")
                        if 'original_ocr' in ocr_timing:
                            print(f"      - 原始图像OCR: {ocr_timing['original_ocr']:.3f}s")
                        if 'preprocessing' in ocr_timing:
                            print(f"      - 图像预处理: {ocr_timing['preprocessing']:.3f}s")
                        if 'preprocessed_ocr' in ocr_timing:
                            print(f"      - 预处理图像OCR: {ocr_timing['preprocessed_ocr']:.3f}s")

                    # 识别结果
                    if attempt.get('success') and 'result' in attempt:
                        res = attempt['result']
                        print(f"   ✅ 识别结果: '{res.get('text', '')}' (置信度: {res.get('confidence', 0):.3f}, 方法: {res.get('method', '')})")
                    elif not attempt.get('success'):
                        print(f"   ❌ 失败原因: {attempt.get('error', '未知错误')}")

            print("="*60)

            # 根据结果构建标准返回格式
            if result['success']:
                # 成功情况
                response_data = {
                    "code": 200,
                    "message": "识别成功",
                    "data": {
                        "game_id": result.get('game_id'),
                        "game_name": result.get('game_name'),
                        "roi_name": result.get('roi_name'),
                        "text": result.get('text'),
                        "confidence": result.get('confidence'),
                        "recognition_success": result.get('recognition_success'),
                        "attempts": result.get('attempts'),
                        "method": result.get('method'),
                        "image_size": result.get('image_size'),
                        "roi_coordinates": result.get('roi_coordinates'),
                        "request_time": result.get('request_time'),
                        "timing": result.get('timing')
                    }
                }
                return jsonify(response_data), 200
            else:
                # 失败情况 - 需要判断错误类型
                error_msg = result.get('error', '未知错误')

                # 判断错误类型并设置相应的错误码
                if '截图' in error_msg or 'ffmpeg' in error_msg or '流媒体' in error_msg:
                    error_code = 802  # 截图失败
                elif 'OCR' in error_msg or '识别' in error_msg:
                    error_code = 801  # 识别失败
                else:
                    error_code = 803  # 其他错误

                response_data = {
                    "code": error_code,
                    "message": error_msg,
                    "data": {
                        "game_id": result.get('game_id'),
                        "roi_name": result.get('roi_name'),
                        "request_time": result.get('request_time'),
                        "timing": result.get('timing')
                    }
                }
                return jsonify(response_data), 400

        except Exception as e:
            error_msg = f"服务器内部错误: {str(e)}"
            request_time = time.time() - request_start_time

            error_result = {
                "code": 803,
                "message": error_msg,
                "data": {
                    "request_time": request_time
                }
            }
            print(f"\n❌ OCR请求失败: {str(e)} (耗时: {request_time:.3f}s)")
            return jsonify(error_result), 500

    @api.route('/screenshots/info', methods=['GET'])
    def screenshots_info():
        """获取截图目录信息"""
        try:
            from ..core.recognition_service import RecognitionService
            service = RecognitionService()

            screenshot_count = service.stream_capture.get_screenshot_count()
            screenshot_dir = service.stream_capture.get_screenshot_dir()
            max_images = service.stream_capture.max_images

            return jsonify({
                "success": True,
                "screenshot_dir": screenshot_dir,
                "current_count": screenshot_count,
                "max_count": max_images,
                "usage_percentage": round((screenshot_count / max_images) * 100, 1)
            }), 200

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取截图信息失败: {str(e)}"
            }), 500
    
    @api.route('/games', methods=['GET'])
    def get_games():
        """获取所有游戏配置"""
        try:
            games_info = {}
            for game_id, game_config in recognition_service.config_manager.games.items():
                games_info[game_id] = {
                    "name": game_config.name,
                    "rois": list(game_config.rois.keys())
                }
            
            return jsonify({
                "success": True,
                "games": games_info
            }), 200
            
        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取游戏配置失败: {str(e)}"
            }), 500
    
    @api.route('/games/<game_id>/rois', methods=['GET'])
    def get_game_rois(game_id):
        """获取指定游戏的ROI配置"""
        try:
            game_config = recognition_service.config_manager.get_game_config(game_id)
            
            rois_info = recognition_service.config_manager.list_rois(game_id)
            
            return jsonify({
                "success": True,
                "game_id": game_id,
                "game_name": game_config.name,
                "rois": rois_info
            }), 200
            
        except ValueError as e:
            return jsonify({
                "success": False,
                "error": str(e)
            }), 404
        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取ROI配置失败: {str(e)}"
            }), 500
    
    @api.route('/health', methods=['GET'])
    def health_check():
        """健康检查接口"""
        return jsonify({
            "success": True,
            "status": "healthy",
            "service": "OCR Recognition Service",
            "timestamp": time.time(),
            "games_loaded": len(recognition_service.config_manager.games)
        }), 200
    
    return api
