"""
OCR识别引擎
"""

import time
from paddleocr import PaddleOCR
from typing import <PERSON><PERSON>, Optional, Dict
from ..config.models import OCRResult
from ..utils.image_processor import ImageProcessor


class OCREngine:
    """OCR识别引擎"""
    
    def __init__(self):
        # 初始化OCR（优化数字识别）
        self.image_processor = ImageProcessor()
        self.ocr = None
        self._init_ocr()

    def _init_ocr(self):
        """初始化PaddleOCR实例"""
        try:
            # 检测GPU可用性并设置设备
            device = "gpu" if self._detect_gpu_availability() else "cpu"
            print(f"🔧 设备检测结果: {device.upper()}")
            
            self.ocr = PaddleOCR(
                use_doc_orientation_classify=False,
                use_doc_unwarping=False,
                use_textline_orientation=False,
                lang='en',
                device=device,  # 使用device参数控制GPU/CPU
            )
            print(f"✅ PaddleOCR初始化成功 ({device.upper()}模式)")

        except Exception as e:
            print(f"❌ PaddleOCR初始化失败: {e}")
            self.ocr = None

    def _detect_gpu_availability(self) -> bool:
        """检测GPU可用性"""
        try:
            import paddle
            return paddle.is_compiled_with_cuda() and paddle.device.cuda.device_count() > 0
        except:
            return False

    def _reinit_ocr_if_needed(self):
        """在出现异常时重新初始化OCR"""
        try:
            print("🔄 尝试重新初始化PaddleOCR...")
            self._init_ocr()
            return self.ocr is not None
        except Exception as e:
            print(f"❌ 重新初始化失败: {e}")
            return False
    
    def recognize_text(self, image, min_confidence: float = 0.9) -> tuple[str, float, float]:
        """
        识别图像中的文本

        Args:
            image: 输入图像
            min_confidence: 最小置信度阈值

        Returns:
            (识别文本, 置信度, 耗时)
        """
        start_time = time.time()

        # 检查OCR是否已初始化
        if self.ocr is None:
            print("⚠️  OCR未初始化，尝试初始化...")
            if not self._reinit_ocr_if_needed():
                return "", 0.0, time.time() - start_time

        try:
            # 验证图像
            if image is None:
                print("❌ 输入图像为None")
                return "", 0.0, time.time() - start_time

            # 检查图像属性
            if hasattr(image, 'shape'):
                print(f"🖼️  图像形状: {image.shape}")
                print(f"🖼️  图像类型: {image.dtype}")

                # 检查图像是否为空或太小
                if image.shape[0] < 10 or image.shape[1] < 10:
                    print("⚠️  图像太小，可能无法识别")
                    return "", 0.0, time.time() - start_time

                # 检查图像是否全黑或全白
                mean_val = image.mean()
                print(f"🖼️  图像平均亮度: {mean_val:.2f}")
                if mean_val < 5 or mean_val > 250:
                    print("⚠️  图像可能全黑或全白")

            # 保存调试图像（可选）
            try:
                import cv2
                debug_path = f"tmp/debug_roi_{int(time.time() * 1000)}.jpg"
                cv2.imwrite(debug_path, image)
                print(f"🐛 调试图像已保存: {debug_path}")
            except Exception as save_error:
                print(f"⚠️  保存调试图像失败: {save_error}")

            # 图像预处理和验证
            processed_image = self._prepare_image_for_ocr(image)
            if processed_image is None:
                print("❌ 图像预处理失败")
                return "", 0.0, time.time() - start_time

            print("🔄 开始PaddleOCR识别...")
            result = self.ocr.predict(processed_image)
            print("✅ PaddleOCR识别完成")

            # 调试：打印原始结果（简化版）
            if result:
                print(f"🔍 结果类型: {type(result)}")
                print(f"🔍 结果长度: {len(result) if isinstance(result, list) else 'N/A'}")
                # 只打印关键信息，避免过长输出
                if isinstance(result, list) and len(result) > 0:
                    first_result = result[0]
                    if isinstance(first_result, dict):
                        if 'rec_texts' in first_result:
                            print(f"🔍 识别文本: {first_result['rec_texts']}")
                        if 'rec_scores' in first_result:
                            print(f"🔍 置信度: {first_result['rec_scores']}")
            else:
                print("🔍 PaddleOCR返回空结果")

            if not result:
                print("⚠️  PaddleOCR返回空结果")
                return "", 0.0, time.time() - start_time

            best_text = ""
            best_confidence = 0.0

            # 处理不同格式的返回结果
            try:
                # 方式1: 标准格式 - 列表包含字典
                if isinstance(result, list) and len(result) > 0:
                    for res in result:
                        if isinstance(res, dict):
                            # 格式: {'rec_texts': [...], 'rec_scores': [...]}
                            if 'rec_texts' in res and 'rec_scores' in res:
                                for text, confidence in zip(res['rec_texts'], res['rec_scores']):
                                    processed_text, processed_conf = self._process_ocr_text(text, confidence)
                                    if processed_text and processed_conf > best_confidence:
                                        best_text = processed_text
                                        best_confidence = processed_conf
                            # 格式: {'text': '...', 'confidence': 0.xx}
                            elif 'text' in res and 'confidence' in res:
                                text = res['text']
                                confidence = res['confidence']
                                processed_text, processed_conf = self._process_ocr_text(text, confidence)
                                if processed_text and processed_conf > best_confidence:
                                    best_text = processed_text
                                    best_confidence = processed_conf
                        elif isinstance(res, list):
                            # 格式: [[bbox, (text, confidence)], ...]
                            for item in res:
                                if len(item) >= 2 and isinstance(item[1], (list, tuple)) and len(item[1]) >= 2:
                                    text, confidence = item[1][0], item[1][1]
                                    processed_text, processed_conf = self._process_ocr_text(text, confidence)
                                    if processed_text and processed_conf > best_confidence:
                                        best_text = processed_text
                                        best_confidence = processed_conf

                # 方式2: 直接是结果列表格式
                elif isinstance(result, list):
                    for item in result:
                        if isinstance(item, list) and len(item) >= 2:
                            if isinstance(item[1], (list, tuple)) and len(item[1]) >= 2:
                                text, confidence = item[1][0], item[1][1]
                                processed_text, processed_conf = self._process_ocr_text(text, confidence)
                                if processed_text and processed_conf > best_confidence:
                                    best_text = processed_text
                                    best_confidence = processed_conf

                print(f"✅ 最佳识别结果: '{best_text}' (置信度: {best_confidence:.3f})")

            except Exception as parse_error:
                print(f"⚠️  解析OCR结果时出错: {parse_error}")
                print(f"🔍 尝试简单解析...")

                # 简单解析：尝试从结果中提取任何文本
                result_str = str(result)
                import re
                # 查找可能的数字
                numbers = re.findall(r'\d+\.?\d*', result_str)
                if numbers:
                    best_text = numbers[0]
                    best_confidence = 0.5  # 给一个中等置信度
                    print(f"🔧 简单解析结果: '{best_text}'")

            return best_text, best_confidence, time.time() - start_time

        except Exception as e:
            print(f"❌ OCR识别异常: {e}")
            print(f"🔍 异常类型: {type(e)}")
            print(f"🔍 异常详情: {str(e)}")

            # 如果是RuntimeError且包含"Unknown exception"，尝试重新初始化OCR
            if isinstance(e, RuntimeError) and "Unknown exception" in str(e):
                print("🔄 检测到PaddleOCR内部错误，尝试重新初始化...")
                if self._reinit_ocr_if_needed():
                    print("🔄 重新初始化成功，再次尝试识别...")
                    try:
                        # 重新尝试识别
                        result = self.ocr.predict(processed_image)
                        print("✅ 重新识别成功")

                        # 处理结果（简化版，避免递归）
                        if result and isinstance(result, list) and len(result) > 0:
                            first_result = result[0]
                            if isinstance(first_result, dict) and 'rec_texts' in first_result and 'rec_scores' in first_result:
                                texts = first_result['rec_texts']
                                scores = first_result['rec_scores']
                                if texts and scores:
                                    text = texts[0]
                                    confidence = scores[0]
                                    # 简单的文本清理
                                    import re
                                    cleaned_text = re.sub(r'[^0-9.]', '', text)
                                    print(f"🔄 重新识别结果: '{cleaned_text}' (置信度: {confidence:.3f})")
                                    return cleaned_text, confidence, time.time() - start_time

                        print("⚠️  重新识别后仍无有效结果")

                    except Exception as retry_error:
                        print(f"❌ 重新识别也失败了: {retry_error}")
                else:
                    print("❌ 重新初始化失败")

            # 尝试获取更多异常信息
            import traceback
            print(f"🔍 异常堆栈: {traceback.format_exc()}")

            # 根据异常类型提供建议
            if isinstance(e, RuntimeError):
                if "Unknown exception" in str(e):
                    print("💡 建议: PaddleOCR内部错误，已尝试重新初始化")
                elif "CUDA" in str(e):
                    print("💡 建议: CUDA相关错误，尝试使用CPU模式")
                elif "memory" in str(e).lower():
                    print("💡 建议: 内存不足，尝试减少图像尺寸")
            elif isinstance(e, ValueError):
                print("💡 建议: 输入值错误，检查图像格式和数据类型")
            elif isinstance(e, AttributeError):
                print("💡 建议: 属性错误，可能是PaddleOCR版本问题")

            raise Exception(f"OCR识别失败: {type(e).__name__}: {e}")

    def _prepare_image_for_ocr(self, image):
        """为OCR准备图像，确保格式正确"""
        try:
            import cv2
            import numpy as np

            # 检查图像是否为None
            if image is None:
                print("❌ 输入图像为None")
                return None

            # 确保图像是numpy数组
            if not isinstance(image, np.ndarray):
                print(f"⚠️  图像类型不是numpy数组: {type(image)}")
                return None

            # 检查图像维度
            if len(image.shape) < 2:
                print(f"❌ 图像维度不足: {image.shape}")
                return None

            # 确保图像是3通道BGR格式
            if len(image.shape) == 2:
                # 灰度图转BGR
                image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                print("🔄 灰度图转换为BGR")
            elif len(image.shape) == 3:
                if image.shape[2] == 4:
                    # RGBA转BGR
                    image = cv2.cvtColor(image, cv2.COLOR_RGBA2BGR)
                    print("🔄 RGBA转换为BGR")
                elif image.shape[2] == 3:
                    # 已经是BGR，检查通道顺序
                    pass
                else:
                    print(f"❌ 不支持的通道数: {image.shape[2]}")
                    return None

            # 确保数据类型是uint8
            if image.dtype != np.uint8:
                if image.dtype == np.float32 or image.dtype == np.float64:
                    # 浮点数转uint8
                    if image.max() <= 1.0:
                        image = (image * 255).astype(np.uint8)
                    else:
                        image = np.clip(image, 0, 255).astype(np.uint8)
                else:
                    image = image.astype(np.uint8)
                print(f"🔄 数据类型转换为uint8")

            # 检查图像尺寸，确保不会太大导致内存问题
            h, w = image.shape[:2]
            max_size = 2048  # 最大尺寸限制
            if h > max_size or w > max_size:
                # 等比例缩放
                scale = max_size / max(h, w)
                new_h, new_w = int(h * scale), int(w * scale)
                image = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
                print(f"🔄 图像缩放: {w}x{h} -> {new_w}x{new_h}")

            # 确保图像不会太小
            min_size = 10
            if h < min_size or w < min_size:
                print(f"❌ 图像太小: {w}x{h}")
                return None

            # 确保图像内存连续
            if not image.flags['C_CONTIGUOUS']:
                image = np.ascontiguousarray(image)
                print("🔄 确保内存连续性")

            print(f"✅ 图像预处理完成: {image.shape}, {image.dtype}")
            return image

        except Exception as e:
            print(f"❌ 图像预处理失败: {e}")
            return None

    def _process_ocr_text(self, text: str, confidence: float) -> tuple[str, float]:
        """处理OCR识别的文本"""
        try:
            # 修复数字0的识别错误
            corrected_text = self.image_processor.fix_zero_recognition(text)

            # 只保留数字和小数点
            cleaned_text = self.image_processor.extract_numbers(corrected_text)

            print(f"🔤 文本处理: '{text}' -> '{corrected_text}' -> '{cleaned_text}' (置信度: {confidence:.3f})")

            return cleaned_text, confidence

        except Exception as e:
            print(f"⚠️  文本处理失败: {e}")
            return "", 0.0
    
    def recognize_roi_with_fallback(self, roi_image, min_confidence: float = 0.9, rotation_angle: int = 0) -> tuple[OCRResult, Dict[str, float]]:
        """
        对ROI图像进行识别，支持预处理回退

        Args:
            roi_image: ROI图像
            min_confidence: 最小置信度阈值
            rotation_angle: 旋转角度 (0, 90, 180, 270)

        Returns:
            (OCR识别结果, 耗时统计)
        """
        timing = {}
        start_time = time.time()

        # 应用旋转（如果需要）
        processed_roi_image = self._rotate_image_if_needed(roi_image, rotation_angle)
        if rotation_angle != 0:
            timing['rotation'] = 0.001  # 旋转操作很快，记录一个小值

        # 首先尝试原始图像（可能已旋转）
        text, confidence, ocr_time = self.recognize_text(processed_roi_image, min_confidence)
        timing['original_ocr'] = ocr_time

        # 处理空字符串结果
        if text == "" or text.strip() == "":
            text = "0"
            print(f"🔄 空字符串结果已处理为: '{text}'")

        if confidence >= min_confidence:
            timing['total_recognition'] = time.time() - start_time
            return OCRResult(
                text=text,
                confidence=confidence,
                success=True,
                attempts=1,
                method='original'
            ), timing

        # 如果置信度不够，尝试预处理后的图像
        try:
            preprocess_start = time.time()
            processed_image = self.image_processor.preprocess_roi_for_digits(processed_roi_image)
            timing['preprocessing'] = time.time() - preprocess_start

            processed_text, processed_confidence, processed_ocr_time = self.recognize_text(processed_image, min_confidence)
            timing['preprocessed_ocr'] = processed_ocr_time

            # 处理预处理后的空字符串结果
            if processed_text == "" or processed_text.strip() == "":
                processed_text = "0"
                print(f"🔄 预处理后空字符串结果已处理为: '{processed_text}'")

            # 选择置信度更高的结果
            if processed_confidence > confidence:
                timing['total_recognition'] = time.time() - start_time
                return OCRResult(
                    text=processed_text,
                    confidence=processed_confidence,
                    success=processed_confidence >= min_confidence,
                    attempts=2,
                    method='preprocessed'
                ), timing
            else:
                timing['total_recognition'] = time.time() - start_time
                return OCRResult(
                    text=text,
                    confidence=confidence,
                    success=False,
                    attempts=2,
                    method='original'
                ), timing

        except Exception:
            # 预处理失败，返回原始结果
            timing['total_recognition'] = time.time() - start_time
            return OCRResult(
                text=text,
                confidence=confidence,
                success=False,
                attempts=1,
                method='original'
            ), timing

    def _rotate_image_if_needed(self, image, rotation_angle: int):
        """
        根据需要旋转图像

        Args:
            image: 输入图像
            rotation_angle: 旋转角度 (0, 90, 180, 270)

        Returns:
            旋转后的图像
        """
        if rotation_angle == 0:
            return image

        try:
            import cv2

            # 验证旋转角度
            if rotation_angle not in [0, 90, 180, 270]:
                print(f"⚠️  无效的旋转角度: {rotation_angle}，使用原始图像")
                return image

            # 获取图像尺寸
            h, w = image.shape[:2]

            # 计算旋转中心
            center = (w // 2, h // 2)

            # 创建旋转矩阵
            rotation_matrix = cv2.getRotationMatrix2D(center, rotation_angle, 1.0)

            # 计算旋转后的图像尺寸
            if rotation_angle == 90 or rotation_angle == 270:
                # 90度或270度旋转需要交换宽高
                new_w, new_h = h, w
                # 调整旋转矩阵的平移部分
                rotation_matrix[0, 2] += (new_w - w) / 2
                rotation_matrix[1, 2] += (new_h - h) / 2
            else:
                new_w, new_h = w, h

            # 执行旋转
            rotated_image = cv2.warpAffine(image, rotation_matrix, (new_w, new_h),
                                         flags=cv2.INTER_LINEAR,
                                         borderMode=cv2.BORDER_CONSTANT,
                                         borderValue=(255, 255, 255))  # 白色背景

            print(f"🔄 图像已旋转 {rotation_angle}° ({w}x{h} -> {new_w}x{new_h})")
            return rotated_image

        except Exception as e:
            print(f"❌ 图像旋转失败: {e}")
            return image


