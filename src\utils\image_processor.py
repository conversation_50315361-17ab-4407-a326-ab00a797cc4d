"""
图像处理工具
"""

import cv2
import re
from typing import Optional


class ImageProcessor:
    """图像处理器"""
    
    @staticmethod
    def preprocess_roi_for_digits(roi_image):
        """
        预处理ROI图像以改善数字识别
        
        Args:
            roi_image: ROI图像
            
        Returns:
            预处理后的图像
        """
        # 转换为灰度图
        gray = cv2.cvtColor(roi_image, cv2.COLOR_BGR2GRAY)

        # 应用高斯模糊减少噪声
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)

        # 自适应阈值二值化
        binary = cv2.adaptiveThreshold(
            blurred, 255, 
            cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )

        # 形态学操作去除噪声
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

        # 转换回BGR格式供OCR使用
        processed = cv2.cvtColor(cleaned, cv2.COLOR_GRAY2BGR)

        return processed
    
    @staticmethod
    def fix_zero_recognition(text: str) -> str:
        """
        修复常见的数字0识别错误
        
        Args:
            text: 原始识别文本
            
        Returns:
            修复后的文本
        """
        # 常见的0被误识别的情况
        replacements = {
            'O': '0',  # 字母O替换为数字0
            'o': '0',  # 小写字母o替换为数字0
            'D': '0',  # 字母D有时被误识别
            'Q': '0',  # 字母Q有时被误识别
            '©': '0',  # 版权符号有时被误识别
            '°': '0',  # 度数符号有时被误识别
        }

        result = text
        for old, new in replacements.items():
            result = result.replace(old, new)

        return result
    
    @staticmethod
    def extract_numbers(text: str) -> str:
        """
        从文本中提取数字和小数点
        
        Args:
            text: 输入文本
            
        Returns:
            只包含数字和小数点的字符串
        """
        return re.sub(r'[^0-9.]', '', text)
    
    @staticmethod
    def load_image(image_path: str):
        """
        加载图像文件
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            图像数据
            
        Raises:
            Exception: 图像加载失败
        """
        image = cv2.imread(image_path)
        if image is None:
            raise Exception(f"无法加载图像: {image_path}")
        return image
    
    @staticmethod
    def extract_roi(image, roi_config) -> Optional[any]:
        """
        从图像中提取ROI区域
        
        Args:
            image: 原始图像
            roi_config: ROI配置
            
        Returns:
            ROI图像
            
        Raises:
            Exception: ROI坐标超出图像边界
        """
        h, w = image.shape[:2]
        
        if not roi_config.is_within_bounds(w, h):
            raise Exception(
                f"ROI坐标超出图像边界。图像尺寸: {w}x{h}, "
                f"ROI: ({roi_config.x1},{roi_config.y1},{roi_config.x2},{roi_config.y2})"
            )
        
        return image[roi_config.y1:roi_config.y2, roi_config.x1:roi_config.x2]
