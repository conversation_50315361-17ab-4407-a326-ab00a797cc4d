#!/usr/bin/env python3
"""
快速启动OCR服务的脚本
"""

import subprocess
import sys
import os

def main():
    """启动服务"""
    print("🚀 启动OCR数字识别服务...")
    
    # 检查配置文件
    if not os.path.exists("game_configs.json"):
        print("❌ 错误: 配置文件 game_configs.json 不存在")
        sys.exit(1)
    
    try:
        # 启动主服务
        subprocess.run([sys.executable, "main.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
