# OCR数字识别服务

基于PaddleOCR的游戏画面数字识别HTTP服务，支持从流媒体中截取帧并进行OCR识别。

## 功能特性

- 🎮 支持多款游戏的配置管理
- 📹 通过ffmpeg从流媒体截取帧
- 🔍 智能OCR识别，支持重试机制
- 🎯 可配置的ROI区域识别
- 🌐 RESTful HTTP API接口
- 📊 置信度评估和结果优化
- 🏗️ 模块化架构，代码结构清晰

## 项目结构

```
├── src/
│   ├── api/           # HTTP API接口
│   │   ├── app.py     # Flask应用
│   │   └── routes.py  # 路由定义
│   ├── core/          # 核心业务逻辑
│   │   ├── ocr_engine.py        # OCR识别引擎
│   │   └── recognition_service.py # 识别服务
│   ├── config/        # 配置管理
│   │   ├── models.py  # 数据模型
│   │   └── manager.py # 配置管理器
│   └── utils/         # 工具模块
│       ├── stream_capture.py    # 流媒体截取
│       └── image_processor.py   # 图像处理
├── main.py            # 主入口文件
├── test_api.py        # API测试脚本
├── game_configs.json  # 游戏配置文件
└── requirements.txt   # 依赖包列表
```

## 系统要求

- Python 3.7+
- ffmpeg (用于流媒体截取)
- 依赖包：见requirements.txt

## 安装步骤

1. 安装Python依赖：
```bash
pip install -r requirements.txt
```

2. 安装ffmpeg：
   - Windows: 下载ffmpeg并添加到PATH
   - Linux: `sudo apt-get install ffmpeg`
   - macOS: `brew install ffmpeg`

## 配置文件

**必须**创建 `game_configs.json` 文件来配置不同游戏的ROI区域：

```json
{
  "game1": {
    "name": "示例游戏1",
    "rois": {
      "score": {"x1": 100, "y1": 50, "x2": 200, "y2": 80},
      "level": {"x1": 300, "y1": 50, "x2": 400, "y2": 80}
    }
  }
}
```

## 启动服务

```bash
python main.py
```

可选参数：
- `--config`: 指定配置文件路径
- `--host`: 服务器主机地址
- `--port`: 服务器端口
- `--debug`: 启用调试模式

服务将在 `http://localhost:5600` 启动。

## API接口

### 1. OCR识别

**POST** `/ocr`

请求体：
```json
{
  "game_id": "game1",
  "stream_url": "rtmp://example.com/live/stream",
  "roi_name": "score"
}
```

响应：
```json
{
  "success": true,
  "game_id": "game1",
  "roi_name": "score",
  "text": "12345",
  "confidence": 0.95,
  "recognition_success": true,
  "attempts": 1,
  "image_size": "1920x1080",
  "roi_coordinates": "(100,50,200,80)"
}
```

### 2. 获取游戏配置

**GET** `/games`

响应：
```json
{
  "success": true,
  "games": {
    "game1": {
      "name": "示例游戏1",
      "rois": ["score", "level"]
    }
  }
}
```

### 3. 获取游戏ROI配置

**GET** `/games/{game_id}/rois`

响应：
```json
{
  "success": true,
  "game_id": "game1",
  "game_name": "示例游戏1",
  "rois": {
    "score": {"x1": 100, "y1": 50, "x2": 200, "y2": 80}
  }
}
```

### 4. 健康检查

**GET** `/health`

## 识别机制

1. **截取帧**: 使用ffmpeg从流媒体截取一帧
2. **ROI提取**: 根据配置提取指定区域
3. **OCR识别**:
   - 首先使用原始ROI图像进行识别
   - 如果置信度 < 0.9，使用预处理后的ROI图像再次识别
   - 选择置信度更高的结果
4. **重试机制**:
   - 如果仍然 < 0.9，重新截图并重复上述过程，最多3次
   - 返回所有尝试中置信度最高的结果
5. **结果优化**: 修复常见识别错误（如O->0）

## 测试

运行测试脚本：
```bash
python test_api.py
```

可选参数：
- `--url`: API服务地址
- `--stream-url`: 用于OCR测试的流媒体地址

## 使用示例

```python
import requests

# OCR识别请求
response = requests.post('http://localhost:5600/ocr', json={
    "game_id": "racing_game",
    "stream_url": "rtmp://your-stream-url",
    "roi_name": "speed"
})

result = response.json()
if result['success']:
    print(f"识别结果: {result['text']}")
    print(f"置信度: {result['confidence']}")
```

## 注意事项

1. 确保ffmpeg已正确安装并在PATH中
2. 流媒体地址必须是ffmpeg支持的格式
3. ROI坐标不能超出图像边界
4. 建议在实际使用前测试和调整ROI坐标
5. 服务默认运行在调试模式，生产环境请关闭debug

## 故障排除

- **ffmpeg错误**: 检查ffmpeg安装和流地址
- **ROI坐标错误**: 检查坐标是否在图像范围内
- **识别精度低**: 调整ROI区域或图像预处理参数
- **服务启动失败**: 5600
